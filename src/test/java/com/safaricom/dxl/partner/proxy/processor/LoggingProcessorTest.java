package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LoggingProcessorTest {

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @InjectMocks
    private LoggingProcessor processor;

    private RequestContext context;
    private Resource resource;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        // Set up headers
        headers = new HttpHeaders();

        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getMethod()).thenReturn(HttpMethod.GET);
        when(request.getHeaders()).thenReturn(headers);

        // Mock the path
        when(request.getPath()).thenReturn(mock(org.springframework.http.server.RequestPath.class));
        when(request.getPath().pathWithinApplication()).thenReturn(mock(org.springframework.http.server.RequestPath.class));
        when(request.getPath().pathWithinApplication().value()).thenReturn("/users");

        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/users");
        resource.setEndpoint("http://user-service/api/users");
        resource.setOperation("GET_USERS");

        // Set up context
        context = new RequestContext(exchange, Mono.empty());
        context.setResource(resource);
    }

    @Test
    @DisplayName("Should add conversation ID when missing")
    void process_MissingConversationId_AddsConversationId() {
        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
                assertNotNull(headers.getFirst(X_CONVERSATION_ID));
                assertFalse(headers.getFirst(X_CONVERSATION_ID).isEmpty());
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should preserve existing conversation ID")
    void process_ExistingConversationId_PreservesConversationId() {
        // Arrange
        String existingConversationId = "existing-conversation-id";
        headers.set(X_CONVERSATION_ID, existingConversationId);

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
                assertEquals(existingConversationId, headers.getFirst(X_CONVERSATION_ID));
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        assertEquals(10, processor.getDefaultOrder());
    }
}
