package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.SessionDetails;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_USER_LOGIN;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for UserLoginProcessor.
 * Tests all methods with various scenarios including edge cases and error conditions.
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class UserLoginProcessorTest {

    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private UserProfileConfig userProfileConfig;

    @Mock
    private WsStarterService starterProperties;

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private RequestContext requestContext;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @Mock
    private Resource resource;

    private UserLoginProcessor userLoginProcessor;

    @BeforeEach
    void setUp() {
        userLoginProcessor = new UserLoginProcessor(cacheRepository, userProfileConfig, starterProperties, responseUtils);
    }

    @Nested
    @DisplayName("Initialization Tests")
    class InitializationTests {

        @Test
        @DisplayName("Should initialize processor successfully")
        void init_WhenCalled_LogsInitialization() {
            // When
            userLoginProcessor.init();

            // Then - No exception should be thrown
            // Logging is tested through integration tests
            assertNotNull(userLoginProcessor);
        }

        @Test
        @DisplayName("Should return correct default order")
        void getDefaultOrder_WhenCalled_ReturnsCorrectOrder() {
            // When
            int order = userLoginProcessor.getDefaultOrder();

            // Then
            assertEquals(40, order);
        }
    }

    @Nested
    @DisplayName("shouldProcessIfEnabled() Tests")
    class ShouldProcessIfEnabledTests {

        @Test
        @DisplayName("Should process when operation is POST_USER_LOGIN")
        void shouldProcessIfEnabled_WithLoginOperation_ReturnsTrue() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn(POST_USER_LOGIN);

            // When
            boolean result = userLoginProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertTrue(result);
        }

        @Test
        @DisplayName("Should not process when operation is not POST_USER_LOGIN")
        void shouldProcessIfEnabled_WithNonLoginOperation_ReturnsFalse() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn("GET_USERS");

            // When
            boolean result = userLoginProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertFalse(result);
        }

        @Test
        @DisplayName("Should not process when operation is null")
        void shouldProcessIfEnabled_WithNullOperation_ReturnsFalse() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn(null);

            // When
            boolean result = userLoginProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertFalse(result);
        }
    }

    @Nested
    @DisplayName("process() Tests")
    class ProcessTests {

        private HttpHeaders headers;

        @BeforeEach
        void setUp() {
            headers = new HttpHeaders();
            when(requestContext.getExchange()).thenReturn(exchange);
            when(exchange.getRequest()).thenReturn(request);
            when(request.getHeaders()).thenReturn(headers);
            when(requestContext.getResource()).thenReturn(resource);
        }

        @Test
        @DisplayName("Should successfully cache session details and return success response")
        void process_WithValidTokenAndConversationId_CachesSessionAndReturnsSuccess() {
            // Given
            String token = "test-token";
            String conversationId = "conv-123";
            String hashedToken = "hashed-token";
            String cacheKey = "ProxyAuth-" + hashedToken;
            String cacheDuration = "PT1H";
            
            headers.set(X_IDENTITY, token);
            headers.set(X_CONVERSATION_ID, conversationId);
            
            when(starterProperties.hashText(token)).thenReturn(hashedToken);
            when(userProfileConfig.getCacheDuration()).thenReturn(cacheDuration);
            when(cacheRepository.setData(eq(cacheKey), any(SessionDetails.class), eq(Duration.parse(cacheDuration))))
                    .thenReturn(Mono.empty());
            
            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponse(eq(headers), eq(resource), eq(HttpStatus.OK), eq("200"), eq("User session cached successfully")))
                    .thenReturn(successResponse);

            // When
            Mono<ProcessingResult> result = userLoginProcessor.process(requestContext);

            // Then
            StepVerifier.create(result)
                    .expectNextMatches(processingResult -> {
                        assertFalse(processingResult.isContinueProcessing());
                        assertEquals(successResponse, processingResult.getResponse());
                        return true;
                    })
                    .verifyComplete();

            verify(cacheRepository).setData(eq(cacheKey), any(SessionDetails.class), eq(Duration.parse(cacheDuration)));
            verify(responseUtils).errorResponse(eq(headers), eq(resource), eq(HttpStatus.OK), eq("200"), eq("User session cached successfully"));
        }

        @Test
        @DisplayName("Should handle missing conversation ID gracefully")
        void process_WithMissingConversationId_CachesSessionWithNullSessionId() {
            // Given
            String token = "test-token";
            String hashedToken = "hashed-token";
            String cacheKey = "ProxyAuth-" + hashedToken;
            String cacheDuration = "PT30M";
            
            headers.set(X_IDENTITY, token);
            // No X_CONVERSATION_ID header set
            
            when(starterProperties.hashText(token)).thenReturn(hashedToken);
            when(userProfileConfig.getCacheDuration()).thenReturn(cacheDuration);
            when(cacheRepository.setData(eq(cacheKey), any(SessionDetails.class), eq(Duration.parse(cacheDuration))))
                    .thenReturn(Mono.empty());
            
            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponse(eq(headers), eq(resource), eq(HttpStatus.OK), eq("200"), eq("User session cached successfully")))
                    .thenReturn(successResponse);

            // When
            Mono<ProcessingResult> result = userLoginProcessor.process(requestContext);

            // Then
            StepVerifier.create(result)
                    .expectNextMatches(processingResult -> {
                        assertFalse(processingResult.isContinueProcessing());
                        assertEquals(successResponse, processingResult.getResponse());
                        return true;
                    })
                    .verifyComplete();

            verify(cacheRepository).setData(eq(cacheKey), argThat((SessionDetails sessionDetails) ->
                sessionDetails.getSessionId() == null && sessionDetails.getTimestamp() != null),
                eq(Duration.parse(cacheDuration)));
        }

        @Test
        @DisplayName("Should handle missing token gracefully")
        void process_WithMissingToken_ProcessesWithNullToken() {
            // Given
            String conversationId = "conv-123";
            String cacheDuration = "PT1H";

            headers.set(X_CONVERSATION_ID, conversationId);
            // No X_IDENTITY header set

            when(starterProperties.hashText(null)).thenReturn("hashed-null");
            when(userProfileConfig.getCacheDuration()).thenReturn(cacheDuration);
            when(cacheRepository.setData(eq("ProxyAuth-hashed-null"), any(SessionDetails.class), eq(Duration.parse(cacheDuration))))
                    .thenReturn(Mono.empty());

            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponse(eq(headers), eq(resource), eq(HttpStatus.OK), eq("200"), eq("User session cached successfully")))
                    .thenReturn(successResponse);

            // When
            Mono<ProcessingResult> result = userLoginProcessor.process(requestContext);

            // Then
            StepVerifier.create(result)
                    .expectNextMatches(processingResult -> {
                        assertFalse(processingResult.isContinueProcessing());
                        assertEquals(successResponse, processingResult.getResponse());
                        return true;
                    })
                    .verifyComplete();

            verify(starterProperties).hashText(null);
        }

        @Test
        @DisplayName("Should handle empty token gracefully")
        void process_WithEmptyToken_ProcessesEmptyToken() {
            // Given
            String emptyToken = "";
            String conversationId = "conv-123";
            String hashedToken = "hashed-empty";
            String cacheKey = "ProxyAuth-" + hashedToken;
            String cacheDuration = "PT2H";

            headers.set(X_IDENTITY, emptyToken);
            headers.set(X_CONVERSATION_ID, conversationId);

            when(starterProperties.hashText(emptyToken)).thenReturn(hashedToken);
            when(userProfileConfig.getCacheDuration()).thenReturn(cacheDuration);
            when(cacheRepository.setData(eq(cacheKey), any(SessionDetails.class), eq(Duration.parse(cacheDuration))))
                    .thenReturn(Mono.empty());

            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponse(eq(headers), eq(resource), eq(HttpStatus.OK), eq("200"), eq("User session cached successfully")))
                    .thenReturn(successResponse);

            // When
            Mono<ProcessingResult> result = userLoginProcessor.process(requestContext);

            // Then
            StepVerifier.create(result)
                    .expectNextMatches(processingResult -> {
                        assertFalse(processingResult.isContinueProcessing());
                        assertEquals(successResponse, processingResult.getResponse());
                        return true;
                    })
                    .verifyComplete();

            verify(cacheRepository).setData(eq(cacheKey), argThat((SessionDetails sessionDetails) ->
                conversationId.equals(sessionDetails.getSessionId()) && sessionDetails.getTimestamp() != null),
                eq(Duration.parse(cacheDuration)));
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should create instance with valid dependencies")
        void constructor_WithValidDependencies_CreatesInstance() {
            // When
            UserLoginProcessor processor = new UserLoginProcessor(cacheRepository, userProfileConfig, starterProperties, responseUtils);

            // Then
            assertNotNull(processor);
        }
    }
}
