package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.ProcessorConfig;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AbstractRequestProcessorTest {

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @Mock
    private ProcessorConfig processorConfig;

    private TestProcessor processor;
    private ConditionalProcessor conditionalProcessor;
    private RequestContext context;
    private Resource resource;

    @BeforeEach
    void setUp() {
        // Create concrete implementations of AbstractRequestProcessor for testing
        processor = spy(new TestProcessor());
        processor.setProcessorConfig(processorConfig);

        conditionalProcessor = spy(new ConditionalProcessor());
        conditionalProcessor.setProcessorConfig(processorConfig);

        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api/test");
        resource.setOperation("TEST_OPERATION");

        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getMethod()).thenReturn(HttpMethod.GET);
        when(request.getHeaders()).thenReturn(new HttpHeaders());
        when(request.getURI()).thenReturn(URI.create("http://example.com/test"));

        // Set up context
        context = new RequestContext(exchange, Mono.just(new byte[0]));
        context.setResource(resource);

        // Set up processor settings
        when(processorConfig.getSetting("TestProcessor", "testSetting")).thenReturn("testValue");
        when(processorConfig.getSetting("TestProcessor", "nonExistentSetting")).thenReturn(null);
        when(processorConfig.getOrder("TestProcessor")).thenReturn(50);
        when(processorConfig.isEnabled("TestProcessor")).thenReturn(true);

        // Set up conditional processor settings
        when(processorConfig.getSetting(eq("ConditionalProcessor"), anyString())).thenReturn(null);
        when(processorConfig.getOrder("ConditionalProcessor")).thenReturn(null);
        when(processorConfig.isEnabled("ConditionalProcessor")).thenReturn(true);

        // Set up configs for testing getConfigs method
        Map<String, String> testConfigs = new HashMap<>();
        testConfigs.put("key1", "value1");
        testConfigs.put("key2", "value2");
        when(processorConfig.getConfigs("TestProcessor", "testConfig")).thenReturn(testConfigs);
        when(processorConfig.getConfigs("TestProcessor", "nonExistentConfig")).thenReturn(null);
    }

    @Test
    @DisplayName("Should return correct processor name")
    void getName_ReturnsCorrectName() {
        assertEquals("TestProcessor", processor.getName());
    }

    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        // Override the mock to return null for order
        when(processorConfig.getOrder(anyString())).thenReturn(null);
        assertEquals(100, processor.getOrder());
    }

    @Test
    @DisplayName("Should return order from settings if available")
    void getOrder_WithOrderInSettings_ReturnsSettingValue() {
        assertEquals(50, processor.getOrder());
    }

    @Test
    @DisplayName("Should return default order if no order in settings")
    void getOrder_WithoutOrderInSettings_ReturnsDefaultOrder() {
        // This test is covered by getDefaultOrder_ReturnsCorrectValue
        // Adding a different assertion to make it unique
        when(processorConfig.getOrder(anyString())).thenReturn(null);
        int order = processor.getOrder();
        assertEquals(100, order);
        assertEquals(processor.getDefaultOrder(), order);
    }

    @Test
    @DisplayName("Should return setting value if available")
    void getSetting_WithExistingSetting_ReturnsValue() {
        assertEquals("testValue", processor.getSetting("testSetting", "defaultValue"));
    }

    @Test
    @DisplayName("Should return default value if setting not available")
    void getSetting_WithNonExistingSetting_ReturnsDefaultValue() {
        assertEquals("defaultValue", processor.getSetting("nonExistentSetting", "defaultValue"));
    }

    @Test
    @DisplayName("Should return null if setting not available and no default provided")
    void getSetting_WithNonExistentSettingNoDefault_ReturnsNull() {
        assertNull(processor.getSetting("nonExistentSetting"));
    }

    @Test
    @DisplayName("Should return null if processorConfig is null")
    void getSetting_WithNullProcessorConfig_ReturnsNull() {
        // Create a processor with null processorConfig
        TestProcessor nullConfigProcessor = new TestProcessor();
        assertNull(nullConfigProcessor.getSetting("testSetting"));
    }

    @Test
    @DisplayName("Should return true if processor should process the request")
    void shouldProcess_WithEnabledProcessor_ReturnsTrue() {
        assertTrue(processor.shouldProcess(context));
    }

    @Test
    @DisplayName("Should return false if processor is disabled")
    void shouldProcess_WithDisabledProcessor_ReturnsFalse() {
        when(processorConfig.isEnabled("TestProcessor")).thenReturn(false);
        assertFalse(processor.shouldProcess(context));
    }

    @Test
    @DisplayName("Should return false if shouldProcessIfEnabled returns false")
    void shouldProcess_WithConditionalProcessor_ReturnsFalse() {
        // Set up context to make conditional processor return false
        context.setAttribute("skipProcessing", true);
        assertFalse(conditionalProcessor.shouldProcess(context));
    }

    @Test
    @DisplayName("Should return true if shouldProcessIfEnabled returns true")
    void shouldProcess_WithConditionalProcessor_ReturnsTrue() {
        // Set up context to make conditional processor return true
        context.setAttribute("skipProcessing", false);
        assertTrue(conditionalProcessor.shouldProcess(context));
    }

    @Test
    @DisplayName("Should return configs if available")
    void getConfigs_WithExistingConfig_ReturnsConfigs() {
        Map<String, String> configs = processor.getConfigs("testConfig");
        assertEquals(2, configs.size());
        assertEquals("value1", configs.get("key1"));
        assertEquals("value2", configs.get("key2"));
    }

    @Test
    @DisplayName("Should return empty map if config not available")
    void getConfigs_WithNonExistentConfig_ReturnsEmptyMap() {
        Map<String, String> configs = processor.getConfigs("nonExistentConfig");
        assertTrue(configs.isEmpty());
    }

    @Test
    @DisplayName("Should return empty map if processorConfig is null")
    void getConfigs_WithNullProcessorConfig_ReturnsEmptyMap() {
        // Create a processor with null processorConfig
        TestProcessor nullConfigProcessor = new TestProcessor();
        Map<String, String> configs = nullConfigProcessor.getConfigs("testConfig");
        assertTrue(configs.isEmpty());
    }

    @Test
    @DisplayName("Should handle exception when retrieving configs")
    void getConfigs_WithException_ReturnsEmptyMap() {
        // Set up processorConfig to throw exception
        when(processorConfig.getConfigs("TestProcessor", "exceptionConfig"))
                .thenThrow(new RuntimeException("Test exception"));

        Map<String, String> configs = processor.getConfigs("exceptionConfig");
        assertTrue(configs.isEmpty());
    }

    @Test
    @DisplayName("Should process request and return result")
    void process_ValidRequest_ReturnsResult() {
        // Create a processor with process method implementation
        ProcessingProcessor processingProcessor = spy(new ProcessingProcessor());
        processingProcessor.setProcessorConfig(processorConfig);

        // Act
        Mono<ProcessingResult> result = processingProcessor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
                assertNull(processingResult.getResponse());
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should skip processing if processor is disabled")
    void process_DisabledProcessor_SkipsProcessing() {
        // Create a processor with process method implementation
        ProcessingProcessor processingProcessor = spy(new ProcessingProcessor());
        processingProcessor.setProcessorConfig(processorConfig);

        // Disable the processor
        when(processorConfig.isEnabled("ProcessingProcessor")).thenReturn(false);

        // Act
        Mono<ProcessingResult> result = processingProcessor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
                assertNull(processingResult.getResponse());
            })
            .verifyComplete();

        // Verify that processInternal was not called
        verify(processingProcessor, never()).processInternal();
    }

    @Test
    @DisplayName("Should return false when shouldProcessBasedOnOperation with null resource")
    void shouldProcessBasedOnOperation_NullResource_ReturnsFalse() {
        // Arrange
        context.setResource(null);
        OperationBasedProcessor operationProcessor = new OperationBasedProcessor();

        // Act
        boolean result = operationProcessor.testShouldProcessBasedOnOperation(context, "TEST_OPERATION", true);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return true when shouldProcessBasedOnOperation matches target operation")
    void shouldProcessBasedOnOperation_MatchingOperation_ReturnsTrue() {
        // Arrange
        OperationBasedProcessor operationProcessor = new OperationBasedProcessor();

        // Act
        boolean result = operationProcessor.testShouldProcessBasedOnOperation(context, "TEST_OPERATION", true);

        // Assert
        assertTrue(result);
    }

    @Test
    @DisplayName("Should return false when shouldProcessBasedOnOperation doesn't match target operation")
    void shouldProcessBasedOnOperation_NonMatchingOperation_ReturnsFalse() {
        // Arrange
        OperationBasedProcessor operationProcessor = new OperationBasedProcessor();

        // Act
        boolean result = operationProcessor.testShouldProcessBasedOnOperation(context, "OTHER_OPERATION", true);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return false when shouldProcessBasedOnOperation matches but shouldProcessOperation is false")
    void shouldProcessBasedOnOperation_MatchingOperationButShouldNotProcess_ReturnsFalse() {
        // Arrange
        OperationBasedProcessor operationProcessor = new OperationBasedProcessor();

        // Act
        boolean result = operationProcessor.testShouldProcessBasedOnOperation(context, "TEST_OPERATION", false);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return true when shouldProcessBasedOnOperation doesn't match and shouldProcessOperation is false")
    void shouldProcessBasedOnOperation_NonMatchingOperationAndShouldNotProcess_ReturnsTrue() {
        // Arrange
        OperationBasedProcessor operationProcessor = new OperationBasedProcessor();

        // Act
        boolean result = operationProcessor.testShouldProcessBasedOnOperation(context, "OTHER_OPERATION", false);

        // Assert
        assertTrue(result);
    }

    // Concrete implementation of AbstractRequestProcessor for testing
    private static class TestProcessor extends AbstractRequestProcessor {

        @Override
        public Mono<ProcessingResult> process(RequestContext context) {
            return Mono.just(ProcessingResult.continueProcessing());
        }

        @Override
        public int getDefaultOrder() {
            return 100;
        }
    }

    // Concrete implementation with conditional processing
    private static class ConditionalProcessor extends AbstractRequestProcessor {

        @Override
        protected boolean shouldProcessIfEnabled(RequestContext context) {
            // Only process if skipProcessing is not set or false
            Boolean skipProcessing = context.getAttribute("skipProcessing");
            return skipProcessing == null || !skipProcessing;
        }

        @Override
        public Mono<ProcessingResult> process(RequestContext context) {
            return Mono.just(ProcessingResult.continueProcessing());
        }

        @Override
        public int getDefaultOrder() {
            return 200;
        }
    }

    // Concrete implementation with process method implementation
    private static class ProcessingProcessor extends AbstractRequestProcessor {

        @Override
        public Mono<ProcessingResult> process(RequestContext context) {
            if (context == null) {
                return Mono.just(ProcessingResult.continueProcessing());
            }

            if (!shouldProcess(context)) {
                return Mono.just(ProcessingResult.continueProcessing());
            }

            return processInternal()
                    .onErrorResume(e -> {
                        // Log error and continue processing
                        return Mono.just(ProcessingResult.continueProcessing());
                    });
        }

        // Internal processing method
        public Mono<ProcessingResult> processInternal() {
            return Mono.just(ProcessingResult.continueProcessing());
        }

        @Override
        public int getDefaultOrder() {
            return 300;
        }
    }

    // Concrete implementation for testing shouldProcessBasedOnOperation method
    private static class OperationBasedProcessor extends AbstractRequestProcessor {

        // Expose the protected method for testing
        public boolean testShouldProcessBasedOnOperation(RequestContext context, String targetOperation, boolean shouldProcessOperation) {
            return shouldProcessBasedOnOperation(context, targetOperation, shouldProcessOperation);
        }

        @Override
        public Mono<ProcessingResult> process(RequestContext context) {
            return Mono.just(ProcessingResult.continueProcessing());
        }

        @Override
        public int getDefaultOrder() {
            return 400;
        }
    }
}
